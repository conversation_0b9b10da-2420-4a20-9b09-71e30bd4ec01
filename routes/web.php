<?php

use App\Http\Controllers\PostbackController;
use App\Models\Conversion;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Str;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/', function () {

    // Http::get('http://127.0.0.1:8001/postback/notify', [
    //     'click_id' => '123456789',
    // ]);
    return view('welcome');
});



Route::group(['prefix' => 'postback'], function() {

    Route::match(['get', 'post'], '/notify', [PostbackController::class, 'notify']);

});

Route::group(['prefix' => 'IVR'], function() {

    Route::match(['get', 'post'], '/{slug}/push-notification', [PostbackController::class, 'notifyIVR']);

});
Route::get('/refactor-null-services', function () {
    // $conversions = Conversion::whereNull('service')->get();
    // foreach ($conversions as $conversion) {
    //     if (Str::contains($conversion->provider, 'qawafi mtn')) {
    //         $conversion->service = 'qawafi-mtn';
    //         $conversion->save();
    //     }
    // }

    return 'Done';
});

Route::get('/resend-postback-clickids', function () {
    $conversions = Conversion::where('service', 'qawafi-mtn')
        ->where('is_postback_sent', 0)
        ->pluck('click_id');

    return [
        'click_ids_count' => $conversions->count(),
        'click_ids' => $conversions->toArray(),
    ];
});