<?php

return [

    'providers' => [

        'mobipiumlink' => [
            'provider_name' => 'mobipiumlink',
            'logging_only' => false,
            'postback_url' => 'http://mobipiumlink.com/conversion/index.php',
            'postback_http_method' => 'GET',
            // what expecting from client side (portals)
            'request_payload' => [
                'jp',
                'source',
            ],
            'click_id_name' => 'jp',
            // customize any parameter before send postback
            // 'from' => 'to'
            'postback_customized_payload' => [
                // 'jp' => 'jp_click',
                // 'another' => 'anoth',
            ],
            'postback_payload_exclude' => [
                //
            ]
        ],

        'mobipiumlink_zain' => [
            'provider_name' => 'mobipiumlink zain',
            'logging_only' => false,
            'postback_url' => 'http://mobipiumlink.com/conversion/index.php',
            'postback_http_method' => 'GET',
            // what expecting from client side (portals)
            'request_payload' => [
                'click_id',
                'ad_forwarded_by_zain',
            ],
            'click_id_name' => 'click_id',
            // customize any parameter before send postback
            // 'from' => 'to'
            'postback_customized_payload' => [
                'click_id' => 'jp',
            ],
            'postback_payload_exclude' => [
                'ad_forwarded_by_zain'
            ]
        ],

        'mobipiumlink_zaytoon_zain' => [
            'provider_name' => 'mobipiumlink zaytoon zain',
            'logging_only' => false,
            'postback_url' => 'https://smobipiumlink.com/conversion/index.php',
            'postback_http_method' => 'GET',
            // what expecting from client side (portals)
            'request_payload' => [
                'click_id',
                'ad_forwarded_by_zain',
            ],
            'click_id_name' => 'click_id',
            // customize any parameter before send postback
            // 'from' => 'to'
            'postback_customized_payload' => [
                'click_id' => 'jp',
            ],
            'postback_payload_exclude' => [
                'ad_forwarded_by_zain'
            ]
        ],

        'mobipiumlink_coursatplus_zain' => [
            'provider_name' => 'mobipiumlink coursatplus zain',
            'logging_only' => false,
            'postback_url' => 'https://smobipiumlink.com/conversion/index.php',
            'postback_http_method' => 'GET',
            // what expecting from client side (portals)
            'request_payload' => [
                'click_id',
                'ad_forwarded_by_zain',
            ],
            'click_id_name' => 'click_id',
            // customize any parameter before send postback
            // 'from' => 'to'
            'postback_customized_payload' => [
                'click_id' => 'jp',
            ],
            'postback_payload_exclude' => [
                'ad_forwarded_by_zain'
            ]
        ],

        'mobipiumlink_gamezone' => [
            'provider_name' => 'mobipiumlink gamezone zain',
            'logging_only' => false,
            'postback_url' => 'http://mobipiumlink.com/conversion/index.php',
            'postback_http_method' => 'GET',
            // what expecting from client side (portals)
            'request_payload' => [
                'click_id',
                'ad_forwarded_by_zain_to_gamezone',
            ],
            'click_id_name' => 'click_id',
            // customize any parameter before send postback
            // 'from' => 'to'
            'postback_customized_payload' => [
                'click_id' => 'jp',
            ],
            'postback_payload_exclude' => [
                'ad_forwarded_by_zain_to_gamezone'
            ]
        ],

        'gg_agency_gamesclub_mtn' => [
            'provider_name' => 'GG agency gamesclub mtn',
            'logging_only' => false,
            'postback_url' => 'https://n.gg.agency/ntf1/',
            'postback_http_method' => 'GET',
            // what expecting from client side (portals)
            'request_payload' => [
                'click_id',
                'gamesclub_ad_forwarded_by_mtn',
                'token',
            ],
            'click_id_name' => 'click_id',
            // customize any parameter before send postback
            // 'from' => 'to'
            'postback_customized_payload' => [
                // 'click_id' => 'jp',
            ],
            'postback_payload_exclude' => [
                'gamesclub_ad_forwarded_by_mtn'
            ]
        ],

        'traffic_company_gamesclub_mtn' => [
            'provider_name' => 'TrafficCompany gamesclub mtn',
            'logging_only' => false,
            'postback_url' => 'https://postback.level23.nl/', // ?currency=USD&handler=11493&hash=7ec0d431c8b1aa80f4e5baf4c9e62be9&tracker={uniqref}
            'postback_http_method' => 'GET',
            // what expecting from client side (portals)
            'request_payload' => [
                'currency',
                'handler',
                'hash',
                'tracker',
                'gamesclub_ad_forwarded_by_mtn_from_traffic_company',
            ],
            'click_id_name' => 'tracker',
            // customize any parameter before send postback
            // 'from' => 'to'
            'postback_customized_payload' => [
                // 'click_id' => 'jp',
            ],
            'postback_payload_exclude' => [
                'gamesclub_ad_forwarded_by_mtn_from_traffic_company'
            ]
        ],

        'gg_agency_zaytoon_zain' => [
            'provider_name' => 'GG agency zaytoon zain',
            'logging_only' => false,
            'postback_url' => 'https://n.gg.agency/ntf1/',
            'postback_http_method' => 'GET',
            // what expecting from client side (portals)
            'request_payload' => [
                'click_id',
                'zaytoon_ad_forwarded_by_zain_gg_agency',
                'token',
            ],
            'click_id_name' => 'click_id',
            // customize any parameter before send postback
            // 'from' => 'to'
            'postback_customized_payload' => [
                // 'click_id' => 'jp',
            ],
            'postback_payload_exclude' => [
                'zaytoon_ad_forwarded_by_zain_gg_agency'
            ]
        ],

        'gg_agency_coursatplus_zain' => [
            'provider_name' => 'GG agency coursatplus zain',
            'logging_only' => false,
            'postback_url' => 'https://n.gg.agency/ntf1/',
            'postback_http_method' => 'GET',
            // what expecting from client side (portals)
            'request_payload' => [
                'click_id',
                'coursatplus_ad_forwarded_by_zain_gg_agency',
                'token',
            ],
            'click_id_name' => 'click_id',
            // customize any parameter before send postback
            // 'from' => 'to'
            'postback_customized_payload' => [
                // 'click_id' => 'jp',
            ],
            'postback_payload_exclude' => [
                'coursatplus_ad_forwarded_by_zain_gg_agency'
            ]
        ],

        'traffic_company_gamesclub_zain' => [
            'provider_name' => 'TrafficCompany gamesclub zain',
            'logging_only' => false,
            'postback_url' => 'https://postback.level23.nl/', // ?currency=USD&handler=11493&hash=7ec0d431c8b1aa80f4e5baf4c9e62be9&tracker={uniqref}
            'postback_http_method' => 'GET',
            // what expecting from client side (portals)
            'request_payload' => [
                'currency',
                'handler',
                'hash',
                'tracker',
                'gamesclub_ad_forwarded_by_zain_from_traffic_company',
            ],
            'click_id_name' => 'tracker',
            // customize any parameter before send postback
            // 'from' => 'to'
            'postback_customized_payload' => [
                // 'click_id' => 'jp',
            ],
            'postback_payload_exclude' => [
                'gamesclub_ad_forwarded_by_zain_from_traffic_company'
            ]
        ],

        'traffic_company_coursatplus_zain' => [
            'provider_name' => 'TrafficCompany coursat zain',
            'logging_only' => false,
            'postback_url' => 'https://postback.level23.nl/', // ?currency=USD&handler=11493&hash=7ec0d431c8b1aa80f4e5baf4c9e62be9&tracker={uniqref}
            'postback_http_method' => 'GET',
            // what expecting from client side (portals)
            'request_payload' => [
                'currency',
                'handler',
                'hash',
                'tracker',
                'coursatplus_ad_forwarded_by_zain_from_traffic_company',
            ],
            'click_id_name' => 'tracker',
            // customize any parameter before send postback
            // 'from' => 'to'
            'postback_customized_payload' => [
                // 'click_id' => 'jp',
            ],
            'postback_payload_exclude' => [
                'coursatplus_ad_forwarded_by_zain_from_traffic_company'
            ]
        ],

        'traffic_company_zaytoon_zain' => [
            'provider_name' => 'TrafficCompany zaytoon zain',
            'logging_only' => false,
            'postback_url' => 'https://postback.level23.nl/', // ?currency=USD&handler=11493&hash=7ec0d431c8b1aa80f4e5baf4c9e62be9&tracker={uniqref}
            'postback_http_method' => 'GET',
            // what expecting from client side (portals)
            'request_payload' => [
                'currency',
                'handler',
                'hash',
                'tracker',
                'zaytoon_ad_forwarded_by_zain_from_traffic_company',
            ],
            'click_id_name' => 'tracker',
            // customize any parameter before send postback
            // 'from' => 'to'
            'postback_customized_payload' => [
                // 'click_id' => 'jp',
            ],
            'postback_payload_exclude' => [
                'zaytoon_ad_forwarded_by_zain_from_traffic_company'
            ]
        ],

        'traffic_company_ivrs_zain' => [
            'provider_name' => 'TrafficCompany IVRs ZAIN',
            'logging_only' => false,
            'postback_url' => 'https://postback.level23.nl/', // ?currency=USD&handler=11493&hash=7ec0d431c8b1aa80f4e5baf4c9e62be9&tracker={uniqref}
            'postback_http_method' => 'GET',
            // what expecting from client side (portals)
            'request_payload' => [
                'currency',
                'handler',
                'hash',
                'tracker',
                'IVR_ad_forwarded_by_zain_traffic_company',
            ],
            'click_id_name' => 'tracker',
            // customize any parameter before send postback
            // 'from' => 'to'
            'postback_customized_payload' => [
                // 'click_id' => 'jp',
            ],
            'postback_payload_exclude' => [
                'IVR_ad_forwarded_by_zain_traffic_company'
            ]
        ],

        'mobipiumlink_ivrs_zain' => [
            'provider_name' => 'Mobipiumlink IVRs ZAIN',
            'logging_only' => false,
            'postback_url' => 'https://smobipiumlink.com/conversion/index.php',
            'postback_http_method' => 'GET',
            // what expecting from client side (portals)
            'request_payload' => [
                'click_id',
                'IVR_ad_forwarded_by_zain_mobipiumlink',
            ],
            'click_id_name' => 'click_id',
            // customize any parameter before send postback
            // 'from' => 'to'
            'postback_customized_payload' => [
                'click_id' => 'jp',
            ],
            'postback_payload_exclude' => [
                'IVR_ad_forwarded_by_zain_mobipiumlink'
            ]
        ],
        
        'gg_agency_knoooz_mtn' => [
            'provider_name' => 'GG agency knoooz mtn',
            'logging_only' => true,
            'postback_url' => 'https://n.gg.agency/ntf1/',
            'postback_http_method' => 'GET',
            // what expecting from client side (portals)
            'request_payload' => [
                'click_id',
                'knoooz_ad_forwarded_by_mtn',
                'token',
            ],
            'click_id_name' => 'click_id',
            // customize any parameter before send postback
            // 'from' => 'to'
            'postback_customized_payload' => [
                // 'click_id' => 'jp',
            ],
            'postback_payload_exclude' => [
                'knoooz_ad_forwarded_by_mtn'
            ]
        ],

        // MTN Qawafi

        'gg_agency_qawafi_mtn' => [
            'provider_name' => 'GG agency qawafi mtn',
            'logging_only' => false,
            'postback_url' => 'https://n.gg.agency/ntf1/',
            'postback_http_method' => 'GET',
            // what expecting from client side (portals)
            'request_payload' => [
                'click_id',
                'qawafi_ad_forwarded_by_mtn_from_gg_agency',
                'token',
            ],
            'click_id_name' => 'click_id',
            // customize any parameter before send postback
            // 'from' => 'to'
            'postback_customized_payload' => [
                // 'click_id' => 'jp',
            ],
            'postback_payload_exclude' => [
                'qawafi_ad_forwarded_by_mtn_from_gg_agency'
            ]
        ],

        'mobipiumlink_qawafi_mtn' => [
            'provider_name' => 'mobipiumlink qawafi mtn',
            'logging_only' => false,
            'postback_url' => 'http://mobipiumlink.com/conversion/index.php',
            'postback_http_method' => 'GET',
            // what expecting from client side (portals)
            'request_payload' => [
                'click_id',
                'qawafi_ad_forwarded_by_mtn_from_mobipiumlink',
            ],
            'click_id_name' => 'click_id',
            // customize any parameter before send postback
            // 'from' => 'to'
            'postback_customized_payload' => [
                'click_id' => 'jp',
            ],
            'postback_payload_exclude' => [
                'qawafi_ad_forwarded_by_mtn_from_mobipiumlink'
            ]
        ],

        'traffic_company_qawafi_mtn' => [
            'provider_name' => 'TrafficCompany qawafi mtn',
            'logging_only' => false,
            'postback_url' => 'https://postback.level23.nl/', // ?currency=USD&handler=11493&hash=7ec0d431c8b1aa80f4e5baf4c9e62be9&tracker={uniqref}
            'postback_http_method' => 'GET',
            // what expecting from client side (portals)
            'request_payload' => [
                'currency',
                'handler',
                'hash',
                'tracker',
                'qawafi_ad_forwarded_by_mtn_from_traffic_company',
            ],
            'click_id_name' => 'tracker',
            // customize any parameter before send postback
            // 'from' => 'to'
            'postback_customized_payload' => [
                // 'click_id' => 'jp',
            ],
            'postback_payload_exclude' => [
                'qawafi_ad_forwarded_by_mtn_from_traffic_company'
            ]
        ],

        // END Mtn Qawafi

        // Zain Qawafi

        'gg_agency_qawafi_zain' => [
            'provider_name' => 'GG agency qawafi zain',
            'logging_only' => false,
            'postback_url' => 'https://n.gg.agency/ntf1/',
            'postback_http_method' => 'GET',
            // what expecting from client side (portals)
            'request_payload' => [
                'click_id',
                'qawafi_ad_forwarded_by_zain_from_gg_agency',
                'token',
            ],
            'click_id_name' => 'click_id',
            // customize any parameter before send postback
            // 'from' => 'to'
            'postback_customized_payload' => [
                // 'click_id' => 'jp',
            ],
            'postback_payload_exclude' => [
                'qawafi_ad_forwarded_by_zain_from_gg_agency'
            ]
        ],

        'mobipiumlink_qawafi_zain' => [
            'provider_name' => 'mobipiumlink qawafi zain',
            'logging_only' => false,
            'postback_url' => 'http://mobipiumlink.com/conversion/index.php',
            'postback_http_method' => 'GET',
            // what expecting from client side (portals)
            'request_payload' => [
                'click_id',
                'qawafi_ad_forwarded_by_zain_from_mobipiumlink',
            ],
            'click_id_name' => 'click_id',
            // customize any parameter before send postback
            // 'from' => 'to'
            'postback_customized_payload' => [
                'click_id' => 'jp',
            ],
            'postback_payload_exclude' => [
                'qawafi_ad_forwarded_by_zain_from_mobipiumlink'
            ]
        ],

        'traffic_company_qawafi_zain' => [
            'provider_name' => 'TrafficCompany qawafi zain',
            'logging_only' => false,
            'postback_url' => 'https://postback.level23.nl/', // ?currency=USD&handler=11493&hash=7ec0d431c8b1aa80f4e5baf4c9e62be9&tracker={uniqref}
            'postback_http_method' => 'GET',
            // what expecting from client side (portals)
            'request_payload' => [
                'currency',
                'handler',
                'hash',
                'tracker',
                'qawafi_ad_forwarded_by_zain_from_traffic_company',
            ],
            'click_id_name' => 'tracker',
            // customize any parameter before send postback
            // 'from' => 'to'
            'postback_customized_payload' => [
                // 'click_id' => 'jp',
            ],
            'postback_payload_exclude' => [
                'qawafi_ad_forwarded_by_zain_from_traffic_company'
            ]
        ],

        // END Zain Qawafi

        // Sudani Qawafi

        'gg_agency_qawafi_sudani' => [
            'provider_name' => 'GG agency qawafi sudani',
            'logging_only' => false,
            'postback_url' => 'https://n.gg.agency/ntf1/',
            'postback_http_method' => 'GET',
            // what expecting from client side (portals)
            'request_payload' => [
                'click_id',
                'qawafi_ad_forwarded_by_sudani_from_gg_agency',
                'token',
            ],
            'click_id_name' => 'click_id',
            // customize any parameter before send postback
            // 'from' => 'to'
            'postback_customized_payload' => [
                // 'click_id' => 'jp',
            ],
            'postback_payload_exclude' => [
                'qawafi_ad_forwarded_by_sudani_from_gg_agency'
            ]
        ],

        'mobipiumlink_qawafi_sudani' => [
            'provider_name' => 'mobipiumlink qawafi sudani',
            'logging_only' => false,
            'postback_url' => 'http://mobipiumlink.com/conversion/index.php',
            'postback_http_method' => 'GET',
            // what expecting from client side (portals)
            'request_payload' => [
                'click_id',
                'qawafi_ad_forwarded_by_sudani_from_mobipiumlink',
            ],
            'click_id_name' => 'click_id',
            // customize any parameter before send postback
            // 'from' => 'to'
            'postback_customized_payload' => [
                'click_id' => 'jp',
            ],
            'postback_payload_exclude' => [
                'qawafi_ad_forwarded_by_sudani_from_mobipiumlink'
            ]
        ],

        'traffic_company_qawafi_sudani' => [
            'provider_name' => 'TrafficCompany qawafi sudani',
            'logging_only' => false,
            'postback_url' => 'https://postback.level23.nl/', // ?currency=USD&handler=11493&hash=7ec0d431c8b1aa80f4e5baf4c9e62be9&tracker={uniqref}
            'postback_http_method' => 'GET',
            // what expecting from client side (portals)
            'request_payload' => [
                'currency',
                'handler',
                'hash',
                'tracker',
                'qawafi_ad_forwarded_by_sudani_from_traffic_company',
            ],
            'click_id_name' => 'tracker',
            // customize any parameter before send postback
            // 'from' => 'to'
            'postback_customized_payload' => [
                // 'click_id' => 'jp',
            ],
            'postback_payload_exclude' => [
                'qawafi_ad_forwarded_by_sudani_from_traffic_company'
            ]
        ],

        // END Zain Qawafi

        'mobipiumlink_gamesclub_ng' => [
            'provider_name' => 'mobipiumlink gamesclub ng mtn',
            'logging_only' => false,
            'postback_url' => 'http://mobipiumlink.com/conversion/index.php',
            'postback_http_method' => 'GET',
            // what expecting from client side (portals)
            'request_payload' => [
                'click_id',
                'gamesclub_ad_forwarded_by_ng_mtn_mobi',
            ],
            'click_id_name' => 'click_id',
            // customize any parameter before send postback
            // 'from' => 'to'
            'postback_customized_payload' => [
                'click_id' => 'jp',
            ],
            'postback_payload_exclude' => [
                'gamesclub_ad_forwarded_by_ng_mtn_mobi'
            ]
        ],

        'gg_agency_gamesclub_ng_mtn' => [
            'provider_name' => 'GG agency Gamesclub Nigeria mtn',
            'logging_only' => false,
            'postback_url' => 'https://n.gg.agency/ntf1/',
            'postback_http_method' => 'GET',
            // what expecting from client side (portals)
            'request_payload' => [
                'click_id',
                'gamesclub_ad_forwarded_by_ng_mtn_gg_agency',
                'token',
            ],
            'click_id_name' => 'click_id',
            // customize any parameter before send postback
            // 'from' => 'to'
            'postback_customized_payload' => [
                // 'click_id' => 'jp',
            ],
            'postback_payload_exclude' => [
                'gamesclub_ad_forwarded_by_ng_mtn_gg_agency'
            ]
        ],

        'gg_agency_gamesclub_zain' => [
            'provider_name' => 'GG agency Gamesclub Zain',
            'logging_only' => false,
            'postback_url' => 'https://n.gg.agency/ntf1/',
            'postback_http_method' => 'GET',
            // what expecting from client side (portals)
            'request_payload' => [
                'click_id',
                'gamesclub_ad_forwarded_by_zain_gg_agency',
                'token',
            ],
            'click_id_name' => 'click_id',
            // customize any parameter before send postback
            // 'from' => 'to'
            'postback_customized_payload' => [
                // 'click_id' => 'jp',
            ],
            'postback_payload_exclude' => [
                'gamesclub_ad_forwarded_by_zain_gg_agency'
            ]
        ],

        'gg_agency_alareef_zain' => [
            'provider_name' => 'GG agency alareef zain',
            'logging_only' => false,
            'postback_url' => 'https://n.gg.agency/ntf1/',
            'postback_http_method' => 'GET',
            // what expecting from client side (portals)
            'request_payload' => [
                'token',
                'click_id',
                'alareef_ad_forwarded_by_zain',
            ],
            'click_id_name' => 'click_id',
            // customize any parameter before send postback
            // 'from' => 'to'
            'postback_customized_payload' => [
                // 'click_id' => 'jp',
            ],
            'postback_payload_exclude' => [
                'alareef_ad_forwarded_by_zain'
            ]
        ],

        // 'mobipiumlink_alareef' => [
        //     'provider_name' => 'mobipiumlink alareef zain',
        //     'logging_only' => false,
        //     'postback_url' => 'http://mobipiumlink.com/conversion/index.php',
        //     'postback_http_method' => 'GET',
        //     // what expecting from client side (portals)
        //     'request_payload' => [
        //         'click_id',
        //         'alareef_ad_forwarded_by_zain',
        //     ],
        //     'click_id_name' => 'click_id',
        //     // customize any parameter before send postback
        //     // 'from' => 'to'
        //     'postback_customized_payload' => [
        //         'click_id' => 'jp',
        //     ],
        //     'postback_payload_exclude' => [
        //         'alareef_ad_forwarded_by_zain'
        //     ]
        // ],

        'gg_agency_booktown' => [
            'provider_name' => 'gg.agency booktown zain',
            'logging_only' => false,
            'postback_url' => 'https://n.gg.agency/ntf1/', // ?token=xxxxxxxxxxx&click_id={click_id}
            'postback_http_method' => 'GET',
            // what expecting from client side (portals)
            'request_payload' => [
                'click_id',
                'booktown_ad_forwarded_by_zain',
                'token',
            ],
            'click_id_name' => 'click_id',
            // customize any parameter before send postback
            // 'from' => 'to'
            'postback_customized_payload' => [
                // 'click_id' => 'jp',
            ],
            'postback_payload_exclude' => [
                'booktown_ad_forwarded_by_zain'
            ]
        ],

        'sudani_gaamek' => [
            'provider_name' => 'gaamek sudani',
            'logging_only' => false,
            'postback_url' => 'https://m.m2888.net/c/p/4d86c9f92646445880bfc8d943193a97',
            'postback_http_method' => 'GET',
            // what expecting from client side (portals)
            'request_payload' => [
                'click_id',
                'ad_forwarded_by_sudani_to_gamezone',
            ],
            'click_id_name' => 'click_id',
            // customize any parameter before send postback
            // 'from' => 'to'
            'postback_customized_payload' => [
                'click_id' => 'txid',
            ],
            'postback_payload_exclude' => [
                'ad_forwarded_by_sudani_to_gamezone'
            ]
        ],

        'sudani_gaamek_latte_ads' => [
            'provider_name' => 'gaamek sudani latte ads',
            'logging_only' => false,
            'postback_url' => 'https://ads.latteads.com/api/adserver/postback',
            'postback_http_method' => 'GET',
            // what expecting from client side (portals)
            'request_payload' => [
                'transaction_id',
                'secureid',
                'ad_forwarded_by_sudani_to_gamezone_by_ads_latte',
            ],
            'click_id_name' => 'transaction_id',
            // customize any parameter before send postback
            // 'from' => 'to'
            'postback_customized_payload' => [
                'click_id' => 'transaction_id',
            ],
            'postback_payload_exclude' => [
                'ad_forwarded_by_sudani_to_gamezone_by_ads_latte'
            ]
        ],

        'sudani_gaamek_vurtux' => [
            'provider_name' => 'gaamek sudani vurtux',
            'logging_only' => false,
            'postback_url' => 'https://vurtux.aftrad-visit.com/track/direct', // https://vurtux.aftrad-visit.com/track/direct?offer_id=11&publisher_id=23&click_id=123456789&source=sudani&sub_source=gaamek
            'postback_http_method' => 'GET',
            // what expecting from client side (portals)
            'request_payload' => [
                'offer_id',
                'publisher_id',
                'click_id',
                'source',
                'sub_source',
                'ad_forwarded_by_sudani_to_gamezone_by_vurtux',
            ],
            'click_id_name' => 'click_id',
            // customize any parameter before send postback
            // 'from' => 'to'
            'postback_customized_payload' => [
                'click_id' => 'click_id',
            ],
            'postback_payload_exclude' => [
                'ad_forwarded_by_sudani_to_gamezone_by_vurtux'
            ]
        ],

        'google' => [
            'provider_name' => 'google',
            'logging_only' => true,
            'postback_url' => 'https://jsonplaceholder.typicode.com/posts/1',
            'postback_http_method' => 'GET',
            // what expecting from client side (portals)
            'request_payload' => [
                'qawaafi_com',
                'gclid'
            ],
            'click_id_name' => 'gclid',
            // customize any parameter before send postback
            // 'from' => 'to'
            'postback_customized_payload' => [
                //
            ],
            'postback_payload_exclude' => [
                 //
            ]
        ],

        'google_knooozy' => [
            'provider_name' => 'google knooozy',
            'logging_only' => true,
            'postback_url' => 'https://jsonplaceholder.typicode.com/posts/1',
            'postback_http_method' => 'GET',
            // what expecting from client side (portals)
            'request_payload' => [
                'gclid',
                'amt',
                'ad_forwarded_by_sudani',
            ],
            'click_id_name' => 'gclid',
            // customize any parameter before send postback
            // 'from' => 'to'
            'postback_customized_payload' => [
                //
            ],
            'postback_payload_exclude' => [
                //
            ]
        ],

        // 'chainese-company' => [
        //     'provider_name' => 'chainese-company',
        //     'logging_only' => false,

        //     'postback_url' => 'http://m.grand0920.com/c/p/62fa2e57b18346d080e44bcb54df0d5d',
        //     'postback_http_method' => 'GET',
        //     // what expecting from client side (portals)
        //     'request_payload' => [
        //         'gclid',
        //         'amt',
        //     ],
        //     'click_id_name' => 'gclid',
        //     // customize any parameter before send postback
        //     // 'from' => 'to'
        //     'postback_customized_payload' => [
        //         'gclid' => 'clickid',
        //     ],
        //     'postback_payload_exclude' => [
        //          //
        //     ]
        // ],
    ]

];
