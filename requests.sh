#!/bin/bash

# Array of click IDs
click_ids=(
    "click-id-1"
    "click-id-2"
    "click-id-3"
    "click-id-4"
    "click-id-5"
    # Add more click IDs as needed
)

# Base URL and common parameters
BASE_URL="https://qawafi.mvas.digital/api/v1.0/MTN/callback"
MSISDN="249923044369"
STATUS="ACT-SB"

echo "Starting HTTP requests with ${#click_ids[@]} click IDs..."
echo "=========================================="

# Loop through each click ID and make the HTTP request
for i in "${!click_ids[@]}"; do
    click_id="${click_ids[$i]}"
    echo "Request $((i+1))/${#click_ids[@]}: Using click_id=${click_id}"
    
    # Make the HTTP POST request using httpie
    http POST "$BASE_URL" \
        MSISDN="$MSISDN" \
        STATUS="$STATUS" \
        click_id="$click_id"
    
    echo "----------------------------------------"
    
    # Optional: Add a small delay between requests to avoid overwhelming the server
    # sleep 1
done

echo "All requests completed!"
