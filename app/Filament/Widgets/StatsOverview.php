<?php

namespace App\Filament\Widgets;

use App\Models\Conversion;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Card;

class StatsOverview extends BaseWidget
{
    protected static ?string $pollingInterval = null;

    protected function getCards(): array
    {
        $all_conversions_count = Conversion::count();
        $all_conversions_latest_created = Conversion::latest()->first()->created_at;

        $success_conversions_count = Conversion::where('is_postback_sent', true)->count();
        $success_conversions_latest_created = Conversion::where('is_postback_sent', true)->latest()->first()?->created_at;

        $failed_conversions_count = Conversion::where('is_postback_sent', false)->count();
        $failed_conversions_latest_created = Conversion::where('is_postback_sent', false)->latest()->first()?->created_at;

        return [
            Card::make('All Conversions', $all_conversions_count)
                ->description("Latest at ${all_conversions_latest_created}")
                ->descriptionIcon('heroicon-s-calendar')
                // ->chart([7, 2, 10, 3, 15, 4, 17])
                ->color('primary'),
            Card::make('Successfully Sent Conversions', $success_conversions_count)
                ->description("Latest at ${success_conversions_latest_created}")
                ->descriptionIcon('heroicon-s-calendar')
                // ->chart([7, 2, 10, 3, 15, 4, 17])
                ->color('success'),
            Card::make('Failed Conversions', $failed_conversions_count)
                ->description("Latest at ${failed_conversions_latest_created}")
                ->descriptionIcon('heroicon-s-calendar')
                // ->chart([7, 2, 10, 3, 15, 4, 17])
                ->color('danger'),
        ];
    }
}
