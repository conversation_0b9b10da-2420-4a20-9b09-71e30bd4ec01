<?php

namespace App\Filament\Widgets;

use App\Models\Conversion;
use Filament\Widgets\LineChartWidget;
use Flowframe\Trend\Trend;
use Flowframe\Trend\TrendValue;

class ServicesConversionChart extends LineChartWidget
{
    protected static ?string $heading = 'All Conversions Per Service';
    protected static ?string $pollingInterval = null;
    protected int | string | array $columnSpan = 'full';

    public ?string $filter = 'qawafi-sudani';

    protected function getData(): array
    {
        $startDate = now()->subDays(29)->startOfDay();
        $endDate = now()->endOfMonth();
        $labels = $this->lastThirtyDays();

        // successful conversion
        $successfulConversionData = Trend::query(Conversion::query()->where('is_postback_sent', true)->where('service', $this->filter))
            ->between(
                start: $startDate,
                end: $endDate,
            )
            ->perDay()
            ->count();

        // failure conversion
        $failureConversionData = Trend::query(Conversion::query()->where('is_postback_sent', false)->where('service', $this->filter))
            ->between(
                start: $startDate,
                end: $endDate,
            )
            ->perDay()
            ->count();

        return [
            'datasets' => [
                [
                    'label' => 'Successful Conversions ('. $successfulConversionData->sum(fn (TrendValue $value) => $value->aggregate) . ')',
                    'data' => $successfulConversionData->map(fn (TrendValue $value) => $value->aggregate),
                    'borderColor' => 'rgb(22, 163, 74)',
                    'tension' => 0.1,
                ],
                [
                    'label' => 'Not Sent Conversions ('. $failureConversionData->sum(fn (TrendValue $value) => $value->aggregate) . ')',
                    'data' => $failureConversionData->map(fn (TrendValue $value) => $value->aggregate),
                    'borderColor' => 'rgb(225, 29, 72)',
                    'tension' => 0.1,
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getFilters(): ?array
    {
        $filters = [];
        // $filtersValues = Conversion::groupby('provider')->pluck('provider')->toArray();
        // $filtersValues = config('tracking.providers');
        // foreach($filtersValues as $value) {
        //     $filters[$value['provider_name']] = $value['provider_name'];
        // }

        $filtersValues = Conversion::groupby('service')->pluck('service')->toArray();
        foreach($filtersValues as $value) {
            $filters[$value] = $value;
        }

        return $filters;
    }

    private function lastThirtyDays()
    {
        $range = [];

        for($i = 29; $i >= 0; $i--) {
            $day = now()->subDays($i)->day;
            $month = $this->getMonthName(now()->subDays($i)->month);

            $range[] = "{$month} {$day}";
        }

        return $range;
    }

    private function getMonthName($moth_digit): string
    {
        return match ($moth_digit){
            1 => 'Jan',
            2 => 'Feb',
            3 => 'Mar',
            4 => 'Apr',
            5 => 'May',
            6 => 'Jun',
            7 => 'Jul',
            8 => 'Aug',
            9 => 'Sep',
            10 => 'Oct',
            11 => 'Nov',
            12 => 'Dec',
        };
    }
}
