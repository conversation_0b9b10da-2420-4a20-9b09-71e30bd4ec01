<?php

namespace App\Filament\Widgets;

use App\Models\Conversion;
use Filament\Widgets\LineChartWidget;
use Flowframe\Trend\Trend;
use Flowframe\Trend\TrendValue;

class ConversionChart extends LineChartWidget
{
    protected static ?string $heading = 'Sucessfull Conversions Linechart';
    protected static ?string $pollingInterval = null;
    protected int | string | array $columnSpan = 'full';

    public ?string $filter = 'lastThirtyDays';

    protected function getData(): array
    {
        $startDate = match($this->filter) {
            'today' => now()->startOfDay(),
            'month' => now()->startOfMonth(),
            'lastThirtyDays' => now()->subDays(29)->startOfDay(),
            'lastTwelveMonthes' => now()->subMonth(11)->startOfMonth(),
            'year' => now()->startOfYear(),
        };

        $endDate = match($this->filter) {
            'today' => now()->endOfDay(),
            'lastThirtyDays' => now()->endOfDay(),
            'month' => now()->endOfMonth(),
            'lastTwelveMonthes' => now()->endOfMonth(),
            'year' => now()->endOfYear(),
        };

        $labels = match($this->filter) {
            // 'week' => 'Last week',
            'today' => [date('d/m/Y')],
            'month' => $this->daysOfThisMonth(),
            'lastThirtyDays' => $this->lastThirtyDays(),
            'lastTwelveMonthes' => $this->lastTwelveMonthes(),
            'year' => ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
        };

        // conversion
        $conversionDataQuery = Trend::query(Conversion::query()->where('is_postback_sent', true))
            ->between(
                start: $startDate,
                end: $endDate,
            );
        $conversionData = match($this->filter) {
            'today' => $conversionDataQuery->perDay()->count(),
            'lastThirtyDays' => $conversionDataQuery->perDay()->count(),
            'month' => $conversionDataQuery->perDay()->count(),
            'lastTwelveMonthes' => $conversionDataQuery->perMonth()->count(),
            'year' => $conversionDataQuery->perMonth()->count(),
        };

        return [
            'datasets' => [
                [
                    'label' => 'Conversions ('. $conversionData->sum(fn (TrendValue $value) => $value->aggregate) . ')',
                    'data' => $conversionData->map(fn (TrendValue $value) => $value->aggregate),
                    'borderColor' => 'rgb(22, 163, 74)',
                    'tension' => 0.1,
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getFilters(): ?array
    {
        return [
            // 'week' => 'Last week',
            'today' => 'Today',
            'month' => 'This month',
            'lastThirtyDays' => 'Last 30 days',
            'lastTwelveMonthes' => 'Last 12 monthes',
            'year' => 'This year',
        ];
    }

    private function daysOfThisMonth()
    {
        $startDay = now()->startOfMonth()->day;
        $endDay = now()->endOfMonth()->day;

        return collect(range($startDay, $endDay))->map(fn ($value) => "${value}th");
    }

    private function lastTwelveMonthes()
    {
        $startMonth = now()->subMonth(11)->month;

        $range = [];

        for($i = $startMonth; $i < 12 + $startMonth; $i++ ) {
            $month = 0;
            if($i <= 12) {
                $month = $i;
            } else {
                $month = $i - 12;
            }
            $range[] = $this->getMonthName($month);
        }

        return $range;
    }

    private function lastThirtyDays()
    {
        $range = [];

        for($i = 29; $i >= 0; $i--) {
            $day = now()->subDays($i)->day;
            $month = $this->getMonthName(now()->subDays($i)->month);

            $range[] = "{$month} {$day}";
        }

        return $range;
    }

    private function getMonthName($moth_digit): string
    {
        return match ($moth_digit){
            1 => 'Jan',
            2 => 'Feb',
            3 => 'Mar',
            4 => 'Apr',
            5 => 'May',
            6 => 'Jun',
            7 => 'Jul',
            8 => 'Aug',
            9 => 'Sep',
            10 => 'Oct',
            11 => 'Nov',
            12 => 'Dec',
        };
    }
}
