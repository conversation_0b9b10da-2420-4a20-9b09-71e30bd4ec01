<?php

namespace App\Filament\Resources\ConversionResource\Pages;

use App\Filament\Resources\ConversionResource;
use Filament\Pages\Actions;
use Filament\Resources\Pages\EditRecord;

class EditConversion extends EditRecord
{
    protected static string $resource = ConversionResource::class;

    protected function getActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }
}
