<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ConversionResource\Pages;
use App\Filament\Resources\ConversionResource\RelationManagers;
use App\Models\Conversion;
use Carbon\Carbon;
use Filament\Forms;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables;
use Filament\Tables\Columns\BooleanColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use PhpParser\Builder\Function_;

class ConversionResource extends Resource
{
    protected static ?string $model = Conversion::class;

    protected static ?string $navigationIcon = 'heroicon-o-collection';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('provider')->searchable()->sortable(),
                TextColumn::make('service')->sortable(),
                TextColumn::make('click_id')->searchable(),
                BooleanColumn::make('is_postback_sent')->label('Postback Sent'),
                TextColumn::make('created_at')->sortable(),
            ])
            ->filters([
                Filter::make('is_postback_sent')
                    ->label('Postback Not Sent')
                    ->toggle()
                    ->query(fn (Builder $query): Builder => $query->where('is_postback_sent', false)),
                Filter::make('operator')
                    ->label('Operator')
                    ->form([
                        Forms\Components\Select::make('operator')
                            ->default(0)
                            ->options(fn () => self::getOperators()),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        
                        return $query->when(
                            $data['operator'],
                            fn (Builder $query, $operator): Builder => $query->whereRaw('LOWER(provider) like ?', ['%' . strtolower($operator) . '%'])
                        );
                    }),
                Filter::make('ads_company')
                    ->label('Ads Company')
                    ->form([
                        Forms\Components\Select::make('ads_company')
                            ->default(0)
                            ->options(fn () => self::getAdsCompanies()),
                    ])->query(function (Builder $query, array $data): Builder {
                        return $query->when(
                            $data['ads_company'],
                            fn (Builder $query, $company): Builder => $query->whereRaw('LOWER(provider) like ?', ['%' . strtolower($company) . '%'])
                        );
                    }),
                SelectFilter::make('service')
                    ->label('Service')
                    ->options(fn () => Conversion::where('service', '<>', null)->groupby('service')->pluck('service', 'service')->toArray()),
                    Filter::make('created_at')
                    ->form([
                        Forms\Components\Select::make('date_option')
                            ->label('Date Option')
                            ->options([
                                'today' => 'Today',
                                'specific' => 'Specific Date',
                                'range' => 'Date Range',
                            ])
                            ->reactive(),
                        Forms\Components\DatePicker::make('specific_date')
                            ->displayFormat('Y-m-d')
                            ->visible(fn (callable $get) => $get('date_option') === 'specific'),
                        Forms\Components\DatePicker::make('created_from')
                            ->displayFormat('Y-m-d')
                            ->visible(fn (callable $get) => $get('date_option') === 'range'),
                        Forms\Components\DatePicker::make('created_until')
                            ->displayFormat('Y-m-d')
                            ->visible(fn (callable $get) => $get('date_option') === 'range'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when($data['date_option'], function ($query, $dateOption) use ($data) {
                            switch ($dateOption) {
                                case 'today':
                                    return $query->whereDate('created_at', Carbon::today());
                                
                                case 'specific':
                                    if (!empty($data['specific_date'])) {
                                        return $query->whereDate('created_at', $data['specific_date']);
                                    }
                                    break;
                                
                                case 'range':
                                    return $query
                                        ->when(
                                            $data['created_from'],
                                            fn (Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                                        )
                                        ->when(
                                            $data['created_until'],
                                            fn (Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                                        );
                            }
                            return $query;
                        });
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                // Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListConversions::route('/'),
            // 'create' => Pages\CreateConversion::route('/create'),
            'view' => Pages\ViewConversion::route('/{record}'),
            // 'edit' => Pages\EditConversion::route('/{record}/edit'),
        ];
    }

    public static function getOperators(): array
    {
        return [
            0 => 'All',
            'zain' => 'Zain',
            'mtn' => 'MTN',
            'sudani' => 'Sudani',
        ];
    }

    public static function getAdsCompanies(): array
    {
        return [
            0 => 'All',
            'mobipium' => 'Mobipium',
            'trafficcompany' => 'Traffic Company',
            'gg agency' => 'GG Agency',
        ];
    }
}