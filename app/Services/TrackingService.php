<?php

namespace App\Services;

use App\Models\Conversion;
use Exception;

class TrackingServices
{

    protected $request;

    protected $provider;

    protected $payload;

    protected $clickID;

    protected $response_status = false;

    protected $response_body;

    public function __construct()
    {

        $this->setRequest();

        $this->setPayload();

        $this->setProviderConfig();

    }

    public function setRequest()
    {
        $this->request = \request();
    }

    public function setPayload()
    {
        $this->payload = $this->request->payload ?? [];

    }

    public function setProviderConfig()
    {
        $payloadKeys = array_keys($this->payload);
        ksort($payloadKeys);
        $payloadKeys = implode('|', $payloadKeys);

        $providers = config('tracking.providers');

        foreach ($providers as $provider) {

            $providerKeys = $provider['request_payload'];
            ksort($providerKeys);
            $providerKeys = implode('|', $providerKeys);
            if (str_contains($payloadKeys, $providerKeys)) {
                $this->provider = $provider;
                return;
            }
        }

        throw new Exception('Can not identify ad provider from given request');
    }

    public function notify()
    {
        $postback_url = $this->provider['postback_url'];
        $method = $this->provider['postback_http_method'];
        $payload = $this->getCustomPayload();

        $response = (new HttpDriver())->send($postback_url, $payload, $method);

        $this->response_status = $response->successful();
        $this->response_body = $response->body();

        return $response;
    }

    public function getCustomPayload()
    {
        $payload = $this->payload;

        foreach ($this->provider['postback_customized_payload'] as $key => $value) {
            if (isset($payload[$key])) {
                $temp = $payload[$key];
                unset($payload[$key]);
                $payload[$value] = $temp;
            }
        }

        foreach ($this->provider['postback_payload_exclude'] as $key) {
            if (isset($payload[$key])) {
                unset($payload[$key]);
            }
        }

        return $payload;
    }

    public function getCLickID()
    {
        $clickIDKey = $this->provider['click_id_name'];

        if (isset($this->payload[$clickIDKey])) {
            return $this->payload[$clickIDKey];
        }

        return null;
    }

    public function getProviderName()
    {
        if (isset($this->provider['provider_name'])) {
            return $this->provider['provider_name'];
        }

        return null;
    }

    public function saveLogToDatabase()
    {
        return Conversion::updateOrCreate([
            'click_id' => $this->getCLickID(),
            'provider' => $this->getProviderName(),
            'is_postback_sent' => $this->response_status,
        ],
        [
            'click_id' => $this->getCLickID(),
            'provider' => $this->getProviderName(),
            'service' => $this->request->service ?? ($this->request->service_id ?? null),
            'request_http_method' => $this->request->method(),
            'request_origin' => $this->request->headers->get('Origin'),
            'request' => json_encode($this->request->all()),
            'is_postback_sent' => $this->response_status,
            'postback_response' => json_encode($this->response_body),
        ]);
    }

    public function canSendPostback()
    {
        return !$this->provider['logging_only'];
    }
}
