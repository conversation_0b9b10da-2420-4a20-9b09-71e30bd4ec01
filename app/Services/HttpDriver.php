<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class HttpDriver {

    protected $response;

    protected $headers = [
        'Accept' => 'application/json',
        'Content-Type' => 'application/json',
    ];

    public function __construct() {

    }


    public function send($postback_url, $payload, $method)
    {
        if(Str::lower($method) == 'post') {
            return Http::withHeaders($this->headers)->post($postback_url, $payload);
        } else {
            return Http::withHeaders($this->headers)->get($postback_url, $payload);
        }
    }

}
