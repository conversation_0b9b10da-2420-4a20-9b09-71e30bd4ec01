<?php

namespace App\Http\Controllers;

use App\Models\Conversion;
use App\Services\TrackingServices;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class PostbackController extends Controller
{

    public function notify(Request $request)
    {
        Log::debug("Notify");
        Log::debug('Method: '. $request->method());
        Log::debug($request->headers->get('Origin') ?? 'unknown origin');
        Log::debug($request->all());

        if(! count($request->all())) {
            return $this->responseFailure('Request is empty, no payload found.', 422);
        }

        if($request->method() == 'GET') {
            return $this->responseFailure('GET method not supported, use POST instead.', 422);
        }

        $this->sendConversion();

        return $this->responseSuccess('Successfully notified');
    }


    public function notifyIVR(Request $request, string $slug) {
        
        Log::debug("Notify");
        Log::debug('IVR: ' .  $slug);
        Log::debug($request->all());
        $clickId = $request->click_id;

        $isLevel23 = count(explode(',', $clickId ?? '')) === 4;

        $payload = [
            'click_id' => $clickId,
            'IVR_ad_forwarded_by_zain_mobipiumlink' => true,
        ];
        if ($isLevel23) {
            $payload = [
                'currency' => 'USD',
                'handler' => env('POSTBACK_LEVEL23_HANDLER'),
                'hash' => env('POSTBACK_LEVEL23_HASH'),
                'tracker' => $clickId,
                "IVR_ad_forwarded_by_zain_traffic_company" => true,
            ];
        }
        if ($clickId) {

            $request->merge([
                'payload' => $payload,
                'service' => "$slug-IVR",
            ]);

            $this->sendConversion();
        }

        return $this->responseSuccess('Successfully notified');
    }

    private function sendConversion()
    {
        try {

            $trackingService = new TrackingServices();

            if ($trackingService->canSendPostback()) {
                $response = $trackingService->notify();
                Log::debug("POSTBACK RESPONSE:");
                Log::debug($response->body());
            } else {
                Log::debug("POSTBACK RESPONSE: (CAN NOT SEND POSTBACK)");
                Log::debug('');
            }

            $trackingService->saveLogToDatabase();

        } catch(Exception $ex) {
            Log::error("POSTBACK ERROR (EX):");
            Log::error($ex->getMessage());

            return $this->responseFailure('Internal server error.', 500);
        }
    }

}
