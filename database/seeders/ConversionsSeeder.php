<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ConversionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $adCompanies = [
            'gg_agency',
            'mobipiumlink',
            'traffic_company'
        ];

        $services = [
            'gamesclub',
            'zaytoon',
            'coursatplus',
            'gamezone',
            'ivrs',
            'alareef',
            'booktown',
            'gaamek',
            'knoooz'
        ];

        $operators = [
            'mtn',
            'zain',
            'sudani'
        ];

        $requestParams = [
            'click_id',
            'jp',
            'tracker',
            'token',
            'currency',
            'handler',
            'hash',
            'ad_forwarded_by_zain',
            'ad_forwarded_by_mtn',
            'ad_forwarded_by_sudani'
        ];

        $contentServices = [
            'games',
            'podcasts',
            'music',
            'videos',
            'news',
            'sports',
            'education',
            'entertainment',
            'lifestyle',
            'health',
            'fitness',
            'books',
            'magazines',
            'comics',
            'kids'
        ];

        $now = Carbon::now();
        $conversions = [];

        // Generate 5000 random conversions
        for ($i = 0; $i < 50000; $i++) {
            // Generate random provider
            $adCompany = $adCompanies[array_rand($adCompanies)];
            $service = $services[array_rand($services)];
            $operator = $operators[array_rand($operators)];
            
            $provider = $adCompany . '_' . $service;
            if (rand(0, 1)) { // 50% chance to add operator
                $provider .= '_' . $operator;
            }

            $clickId = uniqid($provider . '_');
            
            // Generate 1-3 random content services
            $randomServices = collect($contentServices)
                ->random(rand(1, 3))
                ->implode(',');

            // Generate random request payload
            $requestPayload = [];
            $randomParams = array_rand($requestParams, rand(2, 4)); // Get 2-4 random params
            if (!is_array($randomParams)) {
                $randomParams = [$randomParams];
            }
            
            foreach ($randomParams as $paramIndex) {
                $param = $requestParams[$paramIndex];
                switch ($param) {
                    case 'jp':
                    case 'click_id':
                    case 'tracker':
                        $requestPayload[$param] = $clickId;
                        break;
                    case 'token':
                        $requestPayload[$param] = md5(uniqid());
                        break;
                    case 'currency':
                        $requestPayload[$param] = 'USD';
                        break;
                    case 'handler':
                        $requestPayload[$param] = '11493';
                        break;
                    case 'hash':
                        $requestPayload[$param] = md5(uniqid());
                        break;
                    default:
                        if (strpos($param, 'ad_forwarded_by') !== false) {
                            $requestPayload[$param] = '1';
                        }
                }
            }

            $conversions[] = [
                'click_id' => $clickId,
                'provider' => $provider,
                'service' => $randomServices,
                'request_http_method' => 'GET',
                'request_origin' => 'sample_origin',
                'request' => json_encode($requestPayload),
                'postback_response' => json_encode([
                    'status' => 'success',
                    'message' => 'Conversion tracked successfully'
                ]),
                'is_postback_sent' => rand(0, 1),
                'created_at' => $now->subHours(rand(1, 48))->format('Y-m-d H:i:s'),
                'updated_at' => $now->format('Y-m-d H:i:s')
            ];

            if (count($conversions) >= 100) { // Insert in batches of 100
                DB::table('conversions')->insert($conversions);
                $conversions = [];
            }
        }

        // Insert any remaining conversions
        if (!empty($conversions)) {
            DB::table('conversions')->insert($conversions);
        }
    }
} 