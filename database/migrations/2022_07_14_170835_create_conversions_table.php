<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateConversionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('conversions', function (Blueprint $table) {
            $table->id();
            $table->text('click_id')->nullable();
            $table->string('provider')->nullable();
            $table->string('service')->nullable();
            $table->string('request_http_method')->nullable();
            $table->string('request_origin')->nullable();
            $table->json('request')->nullable();
            $table->json('postback_response')->nullable();
            $table->boolean('is_postback_sent')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('conversions');
    }
}
