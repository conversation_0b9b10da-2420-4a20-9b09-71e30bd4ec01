<div wire:id="ZqRsDmVP2hR6HAhdnS35" class="filament-page filament-resources-view-record-page">
    <div class="space-y-6">
        <header
            class="space-y-2 items-start justify-between sm:flex sm:space-y-0 sm:space-x-4  sm:rtl:space-x-reverse sm:py-4 filament-header">
            <h1 class="text-2xl font-bold tracking-tight md:text-3xl filament-header-heading">
                View Conversion Details
            </h1>
        </header>

        <div class="grid grid-cols-1 gap-6 filament-forms-component-container">
            <div class=" col-span-full">
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 filament-forms-component-container m-y-4">
                    <div class="text-lg"><strong>Click ID</strong></div>
                    <div class="lg:cols-2">{{ $record->click_id ?? '-' }}</div>
                </div>
                <hr>

                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 filament-forms-component-container m-y-4">
                    <div class="text-lg"><strong>Provider</strong></div>
                    <div class="lg:cols-2">{{ $record->provider ?? '-' }}</div>
                </div>
                <hr>

                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 filament-forms-component-container m-y-4">
                    <div class="text-lg"><strong>Service</strong></div>
                    <div class="lg:cols-2">{{ $record->service ?? '-' }}</div>
                </div>
                <hr>

                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 filament-forms-component-container m-y-4">
                    <div class="text-lg"><strong>Origin</strong></div>
                    <div class="lg:cols-2">{{ $record->origin ?? '-' }}</div>
                </div>
                <hr>

                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 filament-forms-component-container m-y-4">
                    <div class="text-lg"><strong>Sent At</strong></div>
                    <div class="lg:cols-2">{{ $record->created_at }}</div>
                </div>
                <hr>


                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 filament-forms-component-container m-y-4">
                    <div class="text-lg"><strong>Sent Successfully</strong></div>
                    <div class="lg:cols-2">{{ $record->is_postback_sent ? 'Yes': 'No' }}</div>
                </div>
                <hr>

                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 filament-forms-component-container m-y-4">
                    <div class="text-lg"><strong>Request HTTP Mesthod</strong></div>
                    <div class=""><strong>{{ $record->request_http_method ?? '-' }}</strong></div>
                </div>
                <hr>

                <div class="grid grid-cols-1 lg:grid-cols-1 filament-forms-component-container space-t-4">
                    <div class="text-lg"><strong>Request Body</strong></div>
                    <div class="">@dump(json_decode($record->request))</div>
                </div>
                <hr>

                <div class="grid grid-cols-1 lg:grid-cols-1 filament-forms-component-container">
                    <div class="text-lg"><strong>Postback Response Body</strong></div>
                    <div class="">@dump(json_decode($record->postback_response))</div>
                </div>
                <hr>

            </div>
        </div>

    </div>

</div>
